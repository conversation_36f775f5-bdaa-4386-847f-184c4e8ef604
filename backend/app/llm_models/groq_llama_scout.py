# import logging
# import os

# from langchain.schema.messages import HumanMessage
# from langchain_groq import ChatGroq


# def process_question_with_groq_llm(content, question):
#     try:
#         llm = ChatGroq(groq_api_key=os.getenv("GROK_API_KEY"), model_name="meta-llama/llama-4-scout-17b-16e-instruct")

#         content = content + " | " + question

#         response = llm([HumanMessage(content=content)])

#         return response.content.replace("**", '')
#     except Exception as e:
#         print(e.__cause__)
#         logging.error(msg=f"GROQ API Error: {e}")
#         return ""




import logging
import os
import markdown
import requests
import json


def process_question_with_groq_llm(content, question):
    try:
        url = "https://api.groq.com/openai/v1/chat/completions"
        api_key = os.getenv("GROK_API_KEY")
        if not api_key:
            raise ValueError("GROQ_API_KEY environment variable is not set")

        full_prompt = f"{content} | {question}"

        payload = {
            "model": "meta-llama/llama-4-scout-17b-16e-instruct",
            "messages": [
                {
                    "role": "user",
                    "content": full_prompt
                }
            ]
        }

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }

        response = requests.post(url, headers=headers, data=json.dumps(payload), verify=False)
        response.raise_for_status()  # Raise an error for bad HTTP status

        result = response.json()
        return markdown.markdown(result['choices'][0]['message']['content'])

    except Exception as e:
        logging.error(f"GROQ API Error: {e}")
        return ""
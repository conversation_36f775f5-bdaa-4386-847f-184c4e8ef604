import { ChangeEvent, useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";

import { FileDetails, FormDetails } from "../common/types";
import { ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from "../common/constants";
import { getFiles, uploadFiles } from "../api";
import Card from "./Card";
import { useDispatch, useSelector } from "react-redux";
import {
  resetChat,
  setFileList,
  setSelectedFile,
} from "../redux/slices/HomeSlice";
import { IStore } from "../redux/store";

const Sidebar: React.FC<FormDetails> = ({ setLoading }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dispatch = useDispatch();
  const { flieList, selectedFile } = useSelector(
    (state: IStore) => state.homeReducer
  );
  const [searchTerm, setSearchTerm] = useState("");

  const filteredItems = flieList
    .filter((file) =>
      file.file_name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .reverse();

  // To fetch already uploaded files
  useEffect(() => {
    fetchFiles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchFiles = async () => {
    const files = await getFiles();

    if (files && files.length) {
      dispatch(setFileList(files));
    }
  };

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length) {
      const files = Array.from(event.target.files);
      const validFiles = [];
      for (const file of files) {
        // File Size Validation
        if (file.size > MAX_FILE_SIZE) {
          toast.error(
            `File "${file.name}" exceeds the maximum allowed size of 10MB.`
          );
          break;
        }

        // File Type Validation
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
          toast.error(`File "${file.name} is not a PDF file`);
          break;
        }
        validFiles.push(file);
      }
      dispatch(setSelectedFile(null));
      handleFileUpload(validFiles);
    } else {
      toast.error("Please upload any file.");
    }
  };

  const handleFileUpload = async (filesToUpload: File[]) => {
    const files = await uploadFiles(filesToUpload);

    if (files.status === 200) {
      fetchFiles();
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const generateFileData = (file: FileDetails) => {
    setLoading(true);
    dispatch(setSelectedFile(file));
    if (file._id !== selectedFile._id) {
      dispatch(resetChat());
    }
    setLoading(false);
  };

  return (
    <aside className="sidebar">
      <div className="upload-section">
        <div className="file-selector">
          <div className="browse-section">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search file by name"
              className="file-display"
            />
            <input
              type="file"
              style={{ display: "none" }}
              onChange={handleFileChange}
              ref={fileInputRef}
              accept="application/pdf"
              multiple
            />
            <button onClick={handleButtonClick} className="btn browse-btn">
              +
            </button>
          </div>
        </div>
      </div>
      {filteredItems?.length ? (
        <ul className="file-list">
          {filteredItems.map((file) => (
            <li key={file._id} className="file-item">
              <Card
                title={file.file_name}
                file={file}
                extractedFile={selectedFile}
                generateFileData={generateFileData}
              />
            </li>
          ))}
        </ul>
      ) : (
        <></>
      )}
    </aside>
  );
};

export default Sidebar;

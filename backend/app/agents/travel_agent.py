import json
import logging
import os

import markdown
from langchain.agents import initialize_agent, AgentType
from langchain.memory import ConversationBufferMemory
from langchain.tools import Tool
from langchain_community.chat_message_histories import FileChatMessageHistory

from app.llm_models.custom_groq import Custom<PERSON>roqLL<PERSON>
from app.util import format_success_response


def process_question_with_travel_agent(question):
    try:
        llm = CustomGroqLLM(api_key=os.getenv("GROK_API_KEY"),
                            model_name="meta-llama/llama-4-scout-17b-16e-instruct")

        memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True,
            chat_memory=FileChatMessageHistory("chat_history.json")
        )

        def book_hotel(input: str) -> str:
            try:
                data = json.loads(input.split("\n")[0])

                print("\n\nhotel input data:::::\n\n", data)

                location = data.get("location")
                hotel_name = data.get("hotel_name")
                hotel_category = data.get("hotel_category")
                check_in_date = data.get("check-in date")
                check_out_date = data.get("check-out date")
                no_of_rooms = data.get("no_of_rooms")
                no_of_members = data.get("no_of_members")

                if all([hotel_category, location, check_in_date, check_out_date, no_of_rooms, no_of_members,]) and hotel_name in ["", None, 'Any']:
                    return "Find hotels in [location] available from [check_in_date] to [check_out_date] for [no_of_members]."

                if not all([hotel_name, hotel_category, location, check_in_date, check_out_date, no_of_rooms, no_of_members,]):
                    return "Missing required fields. Please provide 'hotel name', 'hotel category e.g.(5 start, 4 star)', 'location', 'check in date', 'check out date', 'no of rooms', and 'no of members'."

                # Simulate hotel booking logic
                confirmation = (
                    f"""
                    Generate a short hotel booking confirmation message.

                    Input:
                    - Hotel name: {hotel_name}
                    - Hotel category: {hotel_category}
                    - Location: {location}
                    - Check-in date: {check_in_date}
                    - Check-out date: {check_out_date}
                    - No of rooms: {no_of_rooms}
                    - No of members: {no_of_members}
                    
                    
                    The message should confirm the booking and mention all the above details in a natural, friendly tone.
                    """
                )

                return confirmation

            except json.JSONDecodeError:
                return "Invalid JSON input. Please provide a valid JSON string with the correct fields."

        def book_flight(input: str) -> str:
            try:
                data = json.loads(input.split("\n")[0])

                departure_location = data.get("departure_location")
                arrival_location = data.get("arrival_location")
                departure_date = data.get("departure_date")
                arrival_date = data.get("arrival_date")
                no_of_travellers = data.get("no_of_travellers")
                flight_class = data.get("flight_class")


                if not all([departure_location, arrival_location, departure_date, arrival_date, no_of_travellers,]):
                    return "Missing required fields. Please provide 'departure location', 'arrival location', 'departure date', 'arrival date', 'no of travellers'."

                # Simulate flight booking
                return (
                    f"""
                    Generate a short and clear flight booking confirmation message.
                    
                    Input:
                    - Departure location: {departure_location}
                    - Arrival location: {arrival_location}
                    - Departure date: {departure_date}
                    - Arrival date: {arrival_date}
                    - No of travellers: {no_of_travellers}
                    - Flight class: {flight_class}
                    
                    The message should confirm the booking and include all the above details in a natural, friendly tone.
                    """
                )

            except json.JSONDecodeError:
                return "Invalid JSON input. Please provide a valid JSON string with the correct fields."

        def prepare_itinerary(input: str) -> str:
            try:
                data = json.loads(input.split("\n")[0])

                location = data.get("location")
                start_date = data.get("start_date")
                end_date = data.get("end_date")

                if not all([location, start_date, end_date,]):
                    return "Missing required fields. Please provide 'location', 'start date', and 'end date'."

                # Simulate itinerary generation
                return (
                    f"""
                    You are a travel planner. Return the complete itinerary for {location} from {start_date} to {end_date}.
                    
                    The itinerary should include:
                    - A daily plan from the arrival date ({start_date}) to departure date ({end_date})
                    - Morning, afternoon, and evening activities for each day
                    - Recommendations for tourist attractions, local experiences, and food spots
                    - Balanced pacing — not too rushed, not too idle
                    - Local cultural or seasonal events if relevant
                    
                    prepare the detailed itinerary for the trip.
                    
                    Respond only with the itinerary — no extra explanation.
                    """
                )

            except json.JSONDecodeError:
                return "Invalid JSON input. Please provide a valid JSON string with the correct fields."

        def exit_assistant(input_str: str):
            try:
                import os
                if os.path.exists("chat_history.json"):
                    os.remove("chat_history.json")
            except:
                pass
            return format_success_response("Thanks for using the booking assistant.")

        hotel_tool = Tool(
            name="Book Hotel",
            func=book_hotel,
            description="use this tool when user wants to book hotel, required params location, check-in date, check-out date, and number of guests. optional params hotel name, hotel rating"
        )

        flight_tool = Tool(
            name="Book flight",
            func=book_flight,
            description="use this tool when user wants to book flight, required params departure location, arrival location, departure date, arrival date, number of guests. optional params flight class, e.g. (economy, business, first)."
        )

        itinerary_tool = Tool(
            name="Prepare itinerary, Plan trip",
            func=prepare_itinerary,
            description="use this tool when user wants to prepare itinerary, plan trip, give complete plan for the journey and so on, required params location, start_date, end_date."
        )

        exit_tool = Tool(
            name="Exit Agent",
            func=exit_assistant,
            description="Call this tool when the user wants to end the conversation. Trigger on phrases like 'bye', 'exit', 'quit', 'stop', or 'goodbye'. Do not simulate the answer; always call this tool if those words appear."
        )

        tools = [hotel_tool, flight_tool, itinerary_tool, exit_tool]
        agent = initialize_agent(
            tools=tools,
            llm=llm,
            agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
            memory=memory,
            verbose=True
        )

        response = agent.run(question)
        reformat_prompt = f"""
        "You are an assistant that helps users book flights and hotels and itinerary. You have access to tools for flight and hotel booking and itinerary only. 
        You do not have access to booking another tools or APIs. If the user asks to book any other, politely inform them that booking is not supported, 
        but you can assist with flights and hotels and itinerary."

        Original response:
        {response}

        Reformatted message:
        do not give any additional explanation or next step or alternative options or additional information or else something like this.
        
        use markdown language to format the message.
        """
        reformatted = llm.invoke(reformat_prompt)

        return format_success_response(markdown.markdown(reformatted.content))
    except Exception as e:
        logging.exception(msg=str(e))
        reformatted = llm.invoke("Thanks for using the booking assistant.")
        return format_success_response(markdown.markdown(reformatted.content))

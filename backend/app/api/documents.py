import asyncio
import logging
import os
from typing import List

from fastapi import APIRouter, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse

from app.agents.travel_agent import process_question_with_travel_agent
from app.config import settings
from app.crud import (
    save_document,
    get_documents,
    convert_objectid_to_str,
    get_file_name,
    extract_pdf,
    get_file_size,
    get_file_content,
    process_document_async,
    search_similar_chunks,
    get_document_details, format_question,
)
from app.llm_models.distilbert_base_cased_distilled_squad import (
    process_question_with_dbcds,
)
from app.llm_models.minilm_uncased_squad2 import process_question_with_mus
from app.llm_models.roberta_base_squad2 import process_question_with_rbs
from app.llm_models.ollama_llms import process_question_with_ollama
from app.llm_models.llama import process_question_with_llama
from app.models import Document, QNA
from app.util import (
    distance_to_score,
    Models,
    get_llm_response,
    format_error_response,
    format_success_response,
)



router = APIRouter()


@router.post("/documents/")
async def upload_documents(
    files: List[UploadFile] = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """
    API to upload the PDFs, save into the static folder, extract the information and save into the mongodb
    """
    try:
        # Check if all files are PDFs
        for file in files:
            if file.content_type != "application/pdf":
                raise HTTPException(
                    status_code=400, detail="Only PDF files are allowed."
                )

        # Save the uploaded PDFs to the static folder and insert filenames into MongoDB
        saved_files = []
        for file in files:
            file_location = os.path.join(settings.STATIC_FOLDER, file.filename)

            # Save the file to the static folder
            with open(file_location, "wb") as f:
                f.write(await file.read())

            file_size = get_file_size(file_location)

            file_content = extract_pdf(file_location)

            document_data = Document(file_name=file.filename, file_path=settings.PROJECT_URL + file_location,
                file_size=file_size, file_text_content=file_content, file_extracted_details={}, )

            # Save the file name to MongoDB
            document, inserted_id = await save_document(document_data)

            # Create async task in the background
            asyncio.create_task(process_document_async(str(inserted_id), file_content))

            # Store the result
            saved_files.append(
                {
                    "file_name": document.file_name,
                    "file_path": document.file_path,
                    "file_size": document.file_size,
                }
            )
        return {"status": 200, "result": saved_files}

    except Exception as e:
        logging.exception(msg=str(e))
        return format_error_response("Error While Adding the File(s)")


@router.get("/documents/")
async def fetch_documents():
    """
    API to return the documents from the mongodb
    """
    try:
        documents = await get_documents()
        documents = [convert_objectid_to_str(doc) for doc in documents]
        return {"status": 200, "result": documents}
    except Exception as e:
        logging.exception(msg=str(e))
        return format_error_response("Files Not Found")


@router.post("/qna/")
async def question_and_answer(qna: QNA):
    """
    API for extracting the contract information and for the custom question answer.
    """
    try:
        file_content = await get_file_content(qna.fileid)
        if qna.is_qna:
            try:
                llm_response = get_llm_response(
                    content=file_content,
                    question=qna.question,
                    model_name=qna.model_name,
                )
                return format_success_response(llm_response)
            except ValueError as e:
                return format_error_response(str(e))
        else:
            questions = {
                "contract_title": "give me the summary of the contract",
                "contract_type": "give me the contract type",
                "contract_start_date": "give me the starting date of the contract",
                "contract_end_date": "give me the ending date of the contract",
                "contract_amount": "give me the amount for the contract",
                "contract_naisc_code": "give me the NAISC code of the contract",
                "contract_eligibility_criteria": "give me the Eligibility Criteria of the contract",
                "contract_scope": "give me the Scope of the contract",
                "contract_processing_for_biding": "Procedure for bidding for the contract",
            }

            def run_blocking_inference():
                try:
                    return {
                        key: get_llm_response(
                            content=file_content,
                            question=question,
                            model_name=qna.model_name,
                        )
                        for key, question in questions.items()
                    }
                except ValueError as e:
                    return format_error_response(str(e))

            return await asyncio.to_thread(run_blocking_inference)

    except Exception as e:
        logging.exception(msg=str(e))
        return format_error_response(str(e), 500)


@router.get("/documents/{fileid}")
async def get_documents_binary(fileid: str):
    """
    API to get the document by id and send into binary format
    """
    try:
        file_name = await get_file_name(fileid)

        file_path = os.path.join(settings.STATIC_FOLDER, file_name)
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Document not found")

        return FileResponse(
            file_path, media_type="application/pdf", filename=f"{file_name}.pdf"
        )

    except Exception as e:
        logging.exception(msg=str(e))
        return format_error_response(str(e))


@router.post("/search/")
async def search_contracts(question: str, model_name: str = Models.LLAMA.value, is_agent: bool = False):
    """
    API for searching contracts using vector database and LLM.
    Returns relevant contract information based on the search query.
    """
    try:
        if not is_agent:
            # Search for relevant chunks in vector database
            similar_chunks = search_similar_chunks(question)

            if not similar_chunks:
                return format_error_response(
                    "No relevant documents found for your query.", 404
                )

            # Sort chunks by distance (lower distance = more relevant)
            similar_chunks.sort(key=lambda x: x["distance"])

            # Use only top 5 chunks for LLM context
            top_chunks = similar_chunks[:5]

            context = "\n\n".join([chunk["document"] for chunk in top_chunks])


            question = format_question(question)

            try:
                # Get LLM response
                llm_response = get_llm_response(
                    content=context, question=question, model_name=model_name
                )
            except ValueError as e:
                return format_error_response(str(e))

            # Get document details for all chunks
            sources = []
            # Dictionary to track the highest relevance score for each document
            doc_scores = {}

            # Minimum relevance threshold (0.4 means 40% similarity)
            RELEVANCE_THRESHOLD = 0.4

            for chunk in similar_chunks:
                doc_details = await get_document_details(chunk["metadata"]["doc_id"])

                if doc_details:
                    doc_id = doc_details["doc_id"]

                    # Calculate relevance score using inverse distance (higher score for lower distance)
                    relevance_score = 1 / (1 + chunk["distance"])
                    # Only include chunks that meet the relevance threshold
                    if relevance_score >= RELEVANCE_THRESHOLD:
                        # Only keep the chunk with highest relevance score for each document
                        if (
                            doc_id not in doc_scores
                            or relevance_score > doc_scores[doc_id]["relevance_score"]
                        ):
                            doc_scores[doc_id] = {
                                "doc_id": doc_id,
                                "file_name": doc_details["file_name"],
                                "file_size": doc_details["file_size"],
                                "chunk_index": chunk["metadata"]["chunk_index"],
                                "relevance_score": relevance_score,
                            }

            # Convert dictionary to list and sort by relevance score
            sources = list(doc_scores.values())
            sources.sort(key=lambda x: x["relevance_score"], reverse=True)

            # If no relevant chunks found after filtering
            if not sources:
                return format_error_response(
                    "No relevant documents found for your query.", 404
                )

            return format_success_response(llm_response, sources)
        else:
            try:
                response = process_question_with_travel_agent(question)
                return response
            except Exception as e:
                logging.exception(msg=str(e))
            return ""
    except Exception as e:
        logging.exception(msg=str(e))
        return format_error_response(f"Error processing search: {str(e)}", 500)

import { AskAI } from '../api';
import Card from '../components/Card';
import ChatbotPanel from '../components/ChatbotPanel';
import { OptionType } from '../common/types';
import { useDispatch, useSelector } from 'react-redux';
import { IStore } from '../redux/store';
import { setChatMessage, setReferredDocs } from '../redux/slices/AskAISlice';

export default function Search() {
  const { chat, referredDocs } = useSelector(
    (state: IStore) => state.AskAIReducer
  );
  const dispatch = useDispatch();

  const onUserInputSend = async ({
    id,
    message,
    model,
    useAgent,
  }: {
    id?: string;
    message: string;
    model: OptionType | null;
    useAgent?: boolean;
  }) => {
    console.log(id, message, model);
    if (model) {
      dispatch(setChatMessage({ msg: message, isQuestion: true }));
      const res = await AskAI({
        model: model.value,
        question: message,
        is_agent: useAgent || false,
      });
      if (res?.result?.sources) {
        dispatch(setReferredDocs(res?.result?.sources));
      } else {
        dispatch(setReferredDocs([]));
      }
      dispatch(
        setChatMessage({
          msg: res.result?.answer || 'No answer found.',
          isQuestion: false,
        })
      );
    }
  };

  return (
    <div className="askAITabLayout">
      <div className="wrap">
        <div className="askAI-chatbot-panel-wrapper">
          <ChatbotPanel
            onSend={onUserInputSend}
            chat={chat}
            showChecked={true}
          />
        </div>
      </div>

      <aside className="askAITabSidebar">
        <div className="panel-title">Referred Documents</div>
        <div className="list-wrap">
          <ul className="file-list">
            {referredDocs.map((contract) => (
              <li key={contract?.doc_id} className="file-item">
                <Card
                  title={contract?.file_name}
                  file={{
                    _id: contract?.doc_id,
                    file_name: contract?.file_name,
                    file_path: '',
                    file_size: contract?.file_size,
                  }}
                  score={contract?.relevance_score.toFixed(3)}
                />
              </li>
            ))}
          </ul>
        </div>
      </aside>
    </div>
  );
}

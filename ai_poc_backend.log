Device set to use cpu
Some weights of the model checkpoint at deepset/minilm-uncased-squad2 were not used when initializing BertForQuestionAnswering: ['bert.pooler.dense.bias', 'bert.pooler.dense.weight']
- This IS expected if you are initializing BertForQuestionAnswering from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing BertForQuestionAnswering from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Device set to use cpu
Device set to use cpu
INFO:     Started server process [260608]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 98] error while attempting to bind on address ('***************INFO:     ***************:60578 - "GET /api/documents/ HTTP/1.1" 200 OK
INFO:     ***************:38830 - "GET /api/documents/ HTTP/1.1" 200 OK
INFO:     ***************:38830 - "GET /api/documents/ HTTP/1.1" 200 OK
INFO:     ***************:32936 - "GET /api/documents/ HTTP/1.1" 200 OK

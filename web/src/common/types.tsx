export type DocDetails = {
  contract_title: string;
  contract_type: string;
  contract_start_date: string;
  contract_end_date: string;
  contract_amount: string;
  contract_naisc_code: string;
  contract_eligibility_criteria: string;
  contract_scope: string;
  contract_processing_for_biding: string;
};
export type FileDetails = {
  _id: string;
  file_name: string;
  file_size: string;
  file_path: string;
  name?: string;
  size?: string;
  file_processing_status?: string;
};
export type FormDetails = {
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
};

export type OptionType = {
  label: string;
  value: string;
};

export type TitleDetails = {
  title?: string;
};

export type FilesResponse = {
  status: number;
  data: {
    result: FileDetails[];
  };
};

export type ExtractInfoRequest = {
  id: string;
  is_qna: boolean;
  model_name: string;
  question?: string;
};

export interface ContractSource {
  doc_id: string;
  file_name: string;
  file_size: string;
  chunk_index: number;
  relevance_score: number;
}

export interface IChatMessage {
  id?: string;
  msg: string;
  isQuestion: boolean;
  isTag?: boolean;
}

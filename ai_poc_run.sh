#!/bin/bash
#Note: File should be in the same directory where the project directory aidocumentintelligencepoc is located

# Get the absolute path of the script's directory (scripts/)
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"


# Start FastAPI
cd "$SCRIPT_DIR/backend"

# Comment next line if not using virtual env
source venv/bin/activate

nohup uvicorn app.main:app --host *************** --port 8080 > $SCRIPT_DIR/ai_poc_backend.log 2>&1 &

# Start React frontend
cd "$SCRIPT_DIR/web"
npm run build
nohup npx vite preview --host --port 5173 > $SCRIPT_DIR/ai_poc_frontend.log 2>&1 &


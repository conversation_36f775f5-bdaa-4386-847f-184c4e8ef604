import { useState } from 'react';
import Sidebar from '../components/Sidebar';
import PDFViewer from '../components/PDFViewer';
import ChatbotPanel from '../components/ChatbotPanel';
import TitleSection from '../components/TitleSection';
// import CollapsiblePanel from '../components/CollapsiblePanel';
import { OptionType } from '../common/types';
import { getInfo } from '../api';
import { useDispatch, useSelector } from 'react-redux';
import { IStore } from '../redux/store';
import { setChatMessage } from '../redux/slices/HomeSlice';

export default function Home() {
  const [loading, setLoading] = useState<boolean>(false);

  const { selectedFile, chat } = useSelector(
    (state: IStore) => state.homeReducer
  );
  const dispatch = useDispatch();
  const onUserInputSend = async ({
    id,
    message,
    model,
  }: {
    id?: string;
    message: string;
    model: OptionType | null;
  }) => {
    if (id) {
      dispatch(setChatMessage({ msg: message, isQuestion: true }));

      const res = await getInfo({
        id,
        is_qna: true,
        question: message,
        model_name: model?.value || 'rls',
      });
      dispatch(
        setChatMessage({
          msg: res.result?.answer || 'No answer found.',
          isQuestion: false,
        })
      );
    }
  };

  return (
    <div className="layout">
      <Sidebar setLoading={setLoading} />

      <div className="main">
        {loading ? (
          <h2>Loading...</h2>
        ) : selectedFile._id ? (
          <>
            <TitleSection title={selectedFile.file_name} />

            <div className="horizontal-panels">
              {/* <div className="collapsible-panel-wrapper">
                <CollapsiblePanel id={selectedFile?._id} model={model} />
              </div> */}

              <div className="pdf-viewer-wrapper">
                <PDFViewer id={selectedFile?._id} />
              </div>

              <div className="chatbot-panel-wrapper">
                <ChatbotPanel
                  id={selectedFile?._id}
                  onSend={onUserInputSend}
                  tags={[
                    'What is the contract title?',
                    'What is the contract type?',
                  ]}
                  title="AI Assistant"
                  chat={chat}
                  showChecked={false}
                />
              </div>
            </div>
          </>
        ) : (
          <div className="px-2">
            <h2>Please select a file to proceed.</h2>
          </div>
        )}
      </div>
    </div>
  );
}

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import time
import logging
from app.config import settings


logger = logging.getLogger("uvicorn.access")
logger.disabled = False  # To disable logging


def register_middleware(app: FastAPI):

    @app.middleware("http")
    async def custom_logging(request: Request, call_next):
        start_time = time.time()

        response = await call_next(request)
        processing_time = time.time() - start_time

        message = f"{request.method}:{request.url.path} - {response.status_code} completed after {processing_time}"

        return response

    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

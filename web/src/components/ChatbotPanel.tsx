import { useEffect, useRef, useState } from 'react';
import { IChatMessage, OptionType } from '../common/types';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedModel } from '../redux/slices/HomeSlice';
import { IStore } from '../redux/store';
import Select, { StylesConfig } from 'react-select';
import DOMPurify from 'dompurify';

export interface ChatMessages {
  id?: string;
  msg: string;
  isQuestion: boolean;
  isTag?: boolean;
}

export interface ChatbotPanelProps {
  id?: string;
  title?: string;
  tags?: string[];
  onSend: (payload: {
    id?: string;
    message: string;
    model: OptionType | null;
    useAgent?: boolean;
  }) => void;
  chat: IChatMessage[];
  showChecked: boolean;
}

const ChatbotPanel: React.FC<ChatbotPanelProps> = ({
  id,
  title,
  tags = [],
  onSend,
  chat,
  showChecked,
}) => {
  const { modelList, selectedModel } = useSelector(
    (state: IStore) => state.homeReducer
  );
  const dispatch = useDispatch();
  const [msgInput, setMsgInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [iconDisabled, setIconDisabled] = useState(true);
  const [useAgent, setUseAgent] = useState(false);

  const handleSend = async (msg: string) => {
    if (!msg.trim()) return;
    setMsgInput('');
    setLoading(true);

    try {
      await onSend({ id, message: msg, model: selectedModel, useAgent });
      setIconDisabled(true);
    } catch (err) {
      console.error('Failed to get response:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!id) return;
    setMsgInput('');
  }, [id]);

  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
  }, [chat]);

  const customStyles: StylesConfig = {
    control: (provided: Record<string, unknown>) => ({
      ...provided,

      border: '0px',
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected
        ? '#003366' // selected option
        : state.isFocused
        ? '#e0e0e0' // hovered option
        : '#fff', // default
      color: state.isSelected ? '#fff' : '#000',
      padding: 10,
      cursor: 'pointer',
    }),
  };

  return (
    <section className='panel'>
      {title && <div className='panel-title'>{title}</div>}
      <div className='chat-area' ref={containerRef}>
        {chat.map((chat, index) => {
          if (chat.isQuestion) {
            return (
              <div className={`chat-message user`} key={index}>
                {chat.msg}
              </div>
            );
          } else {
            return (
              <div
                className={`chat-message bot`}
                key={index}
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(chat.msg),
                }}
              />
            );
          }
        })}
        {loading && (
          <div className='chat-message bot'>
            <span className='jumping-dots'>
              <span className='dot-1'></span>
              <span className='dot-2'></span>
              <span className='dot-3'></span>
            </span>
          </div>
        )}
        {tags.length > 0 && (
          <div className='sticky-tags'>
            {tags.map((tag, index) => (
              <div
                className='tag-message'
                key={index}
                onClick={() => setMsgInput(tag)}
              >
                {tag}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className='chat-input-container'>
        <div className='chat-input-container-row1'>
          <input
            type='text'
            className='chat-input'
            placeholder='Ask anything...'
            value={msgInput}
            onChange={(e) => {
              setMsgInput(e.target.value);
              setIconDisabled(false);
            }}
            onKeyDown={(e) => e.key === 'Enter' && handleSend(msgInput)}
          />

          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='22'
            height='22'
            fill='none'
            viewBox='0 0 24 24'
            stroke='currentColor'
            strokeWidth='2'
            onClick={() => handleSend(msgInput)}
            className={
              iconDisabled || loading ? 'send-icon-disabled' : 'send-icon'
            }
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              d='M22 2L11 13'
            />
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              d='M22 2L15 22L11 13L2 9L22 2Z'
            />
          </svg>
        </div>
        <div className='chat-input-container-row2'>
          <label className='selector-container'>
            <Select
              options={modelList}
              styles={customStyles}
              className='inline-select'
              value={selectedModel}
              onChange={(newValue) => dispatch(setSelectedModel(newValue))}
              menuPlacement='top'
            />
          </label>

          {showChecked ? (
            <label className='use-agent-checkbox'>
              <input
                type='checkbox'
                checked={useAgent}
                onChange={(e) => setUseAgent(e.target.checked)}
              />
              Use Travel Agent
            </label>
          ) : (
            <></>
          )}
        </div>
      </div>
    </section>
  );
};

export default ChatbotPanel;

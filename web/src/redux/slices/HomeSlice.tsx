import { MODEL_OPTIONS } from '../../common/constants';
import { FileDetails, IChatMessage, OptionType } from '../../common/types';
import { createSlice } from '@reduxjs/toolkit';

export interface IHomeSlice {
  flieList: FileDetails[];
  selectedFile: FileDetails;
  selectedModel: OptionType;
  chat: IChatMessage[];
  modelList: OptionType[];
}

export const initialData: IHomeSlice = {
  flieList: [],
  selectedFile: {
    _id: '',
    file_name: '',
    file_size: '',
    file_path: '',
  },
  selectedModel: MODEL_OPTIONS[2],
  chat: [],
  modelList: MODEL_OPTIONS,
};

const homeSlice = createSlice({
  name: 'homeSlice',
  initialState: initialData,
  reducers: {
    setFileList(state, action) {
      if (action.payload) {
        state.flieList = action.payload;
      }
      return state;
    },
    setSelectedFile(state, action) {
      if (action.payload) {
        state.selectedFile = action.payload;
      }
      return state;
    },
    setChatMessage(state, action) {
      if (action.payload) {
        state.chat.push(action.payload);
      }
      return state;
    },
    resetChat(state) {
      state.chat = [];
      return state;
    },
    setSelectedModel(state, action) {
      if (action.payload) {
        state.selectedModel = action.payload;
      }
      return state;
    },
  },
});

export const {
  resetChat,
  setChatMessage,
  setFileList,
  setSelectedFile,
  setSelectedModel,
} = homeSlice.actions;
export const HomeReducer = homeSlice.reducer;

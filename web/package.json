{"name": "sam_poc_web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/toolbar": "^3.12.0", "@reduxjs/toolkit": "^2.8.1", "axios": "^1.8.4", "dompurify": "^3.2.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-infinite-scroll-component": "^6.1.0", "react-pdf": "^9.2.1", "react-redux": "^9.2.0", "react-router-dom": "^7.5.1", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "reactjs-pdf-reader": "^1.0.12"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.15.2", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-loader-spinner": "^3.1.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.5"}}
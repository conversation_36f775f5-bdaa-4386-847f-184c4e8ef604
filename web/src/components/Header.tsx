import { NavLink } from "react-router-dom";

export default function Header() {
  return (
    <header className="header">
      <div className="header-left d-flex">
        <h1 className="logo">RxAI</h1>
      </div>
      <div className="route-div">
        <NavLink
          to="/"
          className={({ isActive }) => `nav-link ${isActive ? "active" : ""}`}
        >
          Home
        </NavLink>
        <NavLink
          to="/search"
          className={({ isActive }) => `nav-link ${isActive ? "active" : ""}`}
        >
          Ask AI
        </NavLink>
      </div>
    </header>
  );
}

from typing import List

import requests
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.outputs import ChatResult, ChatGeneration
from pydantic import Field


class CustomGroqLLM(BaseChatModel):
    api_key: str = Field(...)
    model_name: str = Field(...)

    def _call_api(self, messages: List[BaseMessage]):
        url = "https://api.groq.com/openai/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        formatted_messages = [
            {
                "role": "user" if isinstance(m, HumanMessage) else "assistant",
                "content": m.content
            }
            for m in messages
        ]

        data = {
            "messages": formatted_messages,
            "model": self.model_name
        }

        response = requests.post(url, headers=headers, json=data, verify=False)
        response_data = response.json()
        print("Full Groq API response:", response_data)
        return response_data["choices"][0]["message"]["content"]

    def _generate(self, messages: List[BaseMessage], **kwargs) -> ChatResult:
        content = self._call_api(messages)
        ai_message = AIMessage(content=content)
        generation = ChatGeneration(message=ai_message)
        return ChatResult(generations=[generation])

    @property
    def _llm_type(self) -> str:
        return "custom-groq"

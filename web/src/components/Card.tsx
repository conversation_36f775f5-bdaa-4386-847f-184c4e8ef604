import { FileDetails } from "../common/types";
import { formatBytes } from "../common/utils";

type CardDetails = {
  title?: string;
  file?: FileDetails;
  selectedFile?: File;
  extractedFile?: FileDetails | null;
  score?: string;
  generateFileData?: (file: FileDetails) => void;
};
export default function Card({
  title,
  file,
  generateFileData,
  selectedFile,
  extractedFile,
  score,
}: CardDetails) {
  return (
    <div
      className={extractedFile?._id === file?._id ? "card selected" : "card"}
      onClick={() => file && generateFileData && generateFileData(file)}
    >
      <p className="file-select-name">{title}</p>
      <p className="file-size">
        {file?.file_size || (selectedFile && formatBytes(selectedFile.size))}
      </p>
      {score && <p className="file-score">Score : {score}</p>}
      {file?.file_processing_status ? (
        <p className={`file-status ${file?.file_processing_status}`}>
          {file?.file_processing_status}
        </p>
      ) : (
        <></>
      )}
    </div>
  );
}

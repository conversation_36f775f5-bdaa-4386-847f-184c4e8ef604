import { createSlice } from "@reduxjs/toolkit";
import { ContractSource, IChatMessage } from "../../common/types";

export interface IAskAISlice {
  chat: IChatMessage[];
  referredDocs: ContractSource[];
}

export const AskAIInitialData: IAskAISlice = {
  chat: [],
  referredDocs: [],
};

export const AskAISlice = createSlice({
  name: "AskAISlice",
  initialState: AskAIInitialData,
  reducers: {
    setChatMessage(state, action) {
      if (action.payload) {
        state.chat.push(action.payload);
      }
      return state;
    },
    setReferredDocs(state, action) {
      if (action.payload) {
        state.referredDocs = action.payload;
      }
      return state;
    },
  },
});

export const { setChatMessage, setReferredDocs } = AskAISlice.actions;
export const AskAIReducer = AskAISlice.reducer;

import asyncio
import logging
import os
import re
import chromadb

from bson import ObjectId
from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import SpacyTextSplitter
from sentence_transformers import SentenceTransformer

from app.config import settings
from app.database import database
from app.models import Document, ProcessingStatus

collection = database["AIDOC"]
client = chromadb.PersistentClient(settings.CHROMA_DB_PATH)


async def save_document(document_data):
    """
    function to save the documents related details into the mongodb
    """
    document = Document(
        file_name=document_data.file_name,
        file_path=document_data.file_path,
        file_size=document_data.file_size,
        file_text_content=document_data.file_text_content,
        file_extracted_details=document_data.file_extracted_details,
        file_processing_status=ProcessingStatus.PENDING

    )
    result = await collection.insert_one(document.dict())
    return document, result.inserted_id


async def get_documents():
    """
    return the documents from the mongodb
    """
    documents = []
    cursor = collection.find(
        {}, {"_id": 1, "file_name": 2, "file_path": 3, "file_size": 4, "file_processing_status": 5}
    )
    async for doc in cursor:
        documents.append(doc)
    return documents


def convert_objectid_to_str(doc):
    if isinstance(doc, dict):
        # Convert ObjectId fields to string
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                doc[key] = str(value)  # Convert ObjectId to string
            elif isinstance(value, dict):  # Recursively handle nested dicts
                convert_objectid_to_str(value)
    return doc


async def get_file_name(fileid):
    """
    function to get the document name from mongodb
    """
    try:
        if isinstance(fileid, str):
            fileid = ObjectId(fileid)
        document = await collection.find_one(
            {"_id": fileid}, {"file_name": 1, "_id": 0}
        )
        if document:
            return document["file_name"]  # Return the document directly
        return None  # If no document is found
    except Exception as e:
        logging.exception(msg=str(e))
        return None


async def set_file_data(fileid, file_data):
    try:
        if isinstance(fileid, str):
            fileid = ObjectId(fileid)
        await collection.update_one(
            {"_id": fileid}, {"$set": {"file_extracted_details": file_data}}
        )
    except Exception as e:
        logging.exception(msg=str(e))


def extract_pdf(file_path):
    """
    function to read the pdf contents using the langchain's PyPDFLoader library
    """
    try:
        loader = PyPDFLoader(file_path=file_path, mode="page", pages_delimiter="")

        docs = []
        docs_lazy = loader.lazy_load()

        for doc in docs_lazy:
            docs.append(doc.page_content)

        return "".join(docs)
    except Exception as e:
        logging.exception(msg=str(e))
        return ""


def convert_bytes(num):
    """
    this function will convert bytes to MB.... GB... etc
    """
    for x in ["KB", "MB", "GB", "TB"]:
        if num < 1024.0:
            return "%3.1f %s" % (num, x)
        num /= 1024.0
        return "%3.1f %s" % (num, x)


def get_file_size(file_path):
    """
    this function will return the file size
    """
    if os.path.isfile(file_path):
        file_info = os.stat(file_path)
        return convert_bytes(file_info.st_size)
    return None


async def get_file_content(fileid):
    """
    function to send the saved document content from mongodb
    """
    try:
        if isinstance(fileid, str):
            fileid = ObjectId(fileid)
        document = await collection.find_one(
            {"_id": fileid}, {"_id": 0, "file_name": 1, "file_extracted_details": 2}
        )
        if document.get("file_extracted_details"):
            return " ".join(
                document["file_extracted_details"].split()
            )  # Return the document directly
        else:
            file_location = os.path.join(
                settings.STATIC_FOLDER, document.get("file_name")
            )
            file_content = extract_pdf(file_location)
            await set_file_data(fileid, file_content)
            return " ".join(file_content.split())  # If no document is found
    except Exception as e:
        logging.exception(msg=str(e))
        return None


def clean_text(text):
    text = re.sub(r"\s+", " ", text)
    text = text.strip()
    return text


def create_chunks(file_content,doc_id, max_words=1000, overlap=100):
    try:
        splitter = SpacyTextSplitter(pipeline="en_core_web_trf", chunk_size=max_words, chunk_overlap=overlap)

        file_content = clean_text(file_content)

        chunks = splitter.split_text(file_content)

        return chunks
    except Exception as e:
        update_processing_status(doc_id, ProcessingStatus.FAILED)
        logging.exception(f"Error creating chunks: {str(e)}")
        raise


def create_embeddings(chunks,doc_id):
    try:
        model = SentenceTransformer("all-MiniLM-L6-v2")

        embeddings = model.encode(chunks, convert_to_tensor=True).tolist()

        return embeddings
    except Exception as e:
        update_processing_status(doc_id, ProcessingStatus.FAILED)
        logging.exception(f"Error creating embeddings: {str(e)}")
        raise


def store_embeddings(chunks, embeddings, doc_id):
    try:
    
        chroma_collection = client.get_or_create_collection(
            name=settings.EMBED_COLLECTION_NAME
        )

        ids = [f"{doc_id}_chunk_{i}" for i in range(len(chunks))]
        metadatas = [{"doc_id": doc_id, "chunk_index": i} for i in range(len(chunks))]

        print("docid processing to save the embeddings to save in the vector db:: ", doc_id)

        chroma_collection.add(
            documents=chunks, embeddings=embeddings, ids=ids, metadatas=metadatas
        )

        print("chroma_collection updated with contract id:  ", doc_id)

        return chroma_collection.get()
    
    except Exception as e:
        update_processing_status(doc_id, ProcessingStatus.FAILED)
        logging.exception(f"Error storing embeddings: {str(e)}")
        raise
  


async def process_document_async(document_id: str, file_content: str):
    """
    Background task to process document chunks, create, and store embeddings
    """
    try:
        print('Function called successfully ',document_id)
        await update_processing_status(document_id, ProcessingStatus.PROCESSING)

        # Move blocking tasks to threads
        chunks = await asyncio.to_thread(create_chunks, file_content,document_id)
        print('chunks created ',document_id)
        embeddings = await asyncio.to_thread(create_embeddings, chunks,document_id)
        print('embeddings created ',document_id)
        await asyncio.to_thread(store_embeddings, chunks, embeddings, document_id)
        print('embeddings stored ',document_id)

        await update_processing_status(document_id, ProcessingStatus.COMPLETED)
        print('Function ended successfully ',document_id)

    except Exception as e:
        await update_processing_status(document_id, ProcessingStatus.FAILED)
        logging.exception(f"Error processing document {document_id}: {str(e)}")


async def update_processing_status(document_id: str, status: ProcessingStatus):
    """
    Update the processing status of a document
    """
    try:
        if isinstance(document_id, str):
            document_id = ObjectId(document_id)

        update_data = {"file_processing_status": status}

        await collection.update_one(
            {"_id": document_id},
            {"$set": update_data}
        )
    except Exception as e:
        logging.exception(f"Error updating processing status: {str(e)}")



def search_similar_chunks(query: str):
    """
    Search for similar chunks in the vector database based on the query.
    Returns a list of similar chunks with their metadata.
    """
    try:
        # Get the collection
        chroma_collection = client.get_or_create_collection(
            name=settings.EMBED_COLLECTION_NAME
        )
        # Create query embedding
        model = SentenceTransformer("all-MiniLM-L6-v2")
        query_embedding = model.encode(query, convert_to_tensor=True).tolist()

        # Search for similar chunks - get all results
        results = chroma_collection.query(
            query_embeddings=[query_embedding],
            n_results=chroma_collection.count(),  # Get all results
            include=["documents", "metadatas", "distances"],
        )

        # Format results
        similar_chunks = []
        if results and results["documents"]:
            for i in range(len(results["documents"][0])):
                similar_chunks.append(
                    {
                        "document": results["documents"][0][i],
                        "metadata": results["metadatas"][0][i],
                        "distance": results["distances"][0][i],
                    }
                )
        return similar_chunks

    except Exception as e:
        logging.exception(f"Error searching similar chunks: {str(e)}")
        return []


async def get_document_details(fileid):
    """
    Get document details including file name and ID from MongoDB
    """
    try:
        if isinstance(fileid, str):
            fileid = ObjectId(fileid)
        document = await collection.find_one(
            {"_id": fileid}, {"_id": 1, "file_name": 1, "file_size" : 1}
        )
        if document:
            return {"doc_id": str(document["_id"]), "file_name": document["file_name"], "file_size" : document["file_size"]}
        return None
    except Exception as e:
        logging.exception(msg=str(e))
        return None


def format_question(question):
    try:
        return f"""
        You are an expert assistant to answer like a chatbot response. Answer the question based only on the following context:
        Answer the question based on the above context: {question}.
        Provide a detailed answer.
        Don’t justify your answers.
        Do not say "according to the context" or "mentioned in the context" or similar.
        Do not give any additional information out of the context. If the answer is not in the context, say "kindly ask the related questions" only in 20 words withput including the heading.
        Give answer like a chatbot assistant not like a llm assistant.
        Do not give answer more than 200 words.
        Do answer the question in markdown format.
        Use smaller Markdown titles by starting headings with '###' instead of '#' or '##'.
        """
    except Exception as e:
        logging.exception(msg=str(e))
        return None
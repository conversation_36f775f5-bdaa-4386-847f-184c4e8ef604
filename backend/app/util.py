import math
from enum import Enum
from typing import List, Dict

from app.llm_models.distilbert_base_cased_distilled_squad import (
    process_question_with_dbcds,
)
from app.llm_models.groq_llama_scout import process_question_with_groq_llm
from app.llm_models.minilm_uncased_squad2 import process_question_with_mus
from app.llm_models.roberta_base_squad2 import process_question_with_rbs
from app.llm_models.ollama_llms import process_question_with_ollama
from app.llm_models.llama import process_question_with_llama


class Models(Enum):
    RBS = "rbs"
    DBCDS = "dbcds"
    MUS = "mus"
    OPENHERMES = "openhermes"
    LLAMA = "llama"
    GROK_LLAMA_SCOUT = "grok_llama_scout"



def distance_to_score(distance, scale=1.0):
    return math.exp(-distance / scale)  # returns score in (0,1]



def get_llm_response(content: str, question: str, model_name: str) -> str:
    """
    Get response from the specified LLM model
    """
    match model_name:
        case Models.RBS.value:
            return process_question_with_rbs(content=content, question=question)
        case Models.DBCDS.value:
            return process_question_with_dbcds(content=content, question=question)
        case Models.MUS.value:
            return process_question_with_mus(content=content, question=question)
        case Models.OPENHERMES.value:
            return process_question_with_ollama(content=content, question=question)
        case Models.LLAMA.value:
            return process_question_with_llama(content=content, question=question)
        case Models.GROK_LLAMA_SCOUT.value:
            return process_question_with_groq_llm(content=content, question=question)
        case _:
            raise ValueError("Select a valid model")


def format_error_response(message: str, status_code: int = 400) -> Dict:
    """
    Format error response
    """
    return {"status": status_code, "result": {"answer": message}}


def format_success_response(answer: str, sources: List = None) -> Dict:
    """
    Format success response
    """
    if sources is None:
        sources = []

    return {"status": 200, "result": {"answer": answer, "sources": sources}}

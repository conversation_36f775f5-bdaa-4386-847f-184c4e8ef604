accelerate==1.6.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.8.1
async-timeout==4.0.3
attrs==25.3.0
backoff==2.2.1
bcrypt==4.3.0
blis==1.3.0
build==1.2.2.post1
cachetools==5.5.2
catalogue==2.0.10
certifi==2025.1.31
charset-normalizer==3.4.1
chromadb==1.0.8
click==8.1.8
cloudpathlib==0.21.0
coloredlogs==15.0.1
confection==0.1.5
curated-tokenizers==0.0.9
curated-transformers==0.1.1
cymem==2.0.11
dataclasses-json==0.6.7
Deprecated==1.2.18
distro==1.9.0
dnspython==2.7.0
durationpy==0.9
en_core_web_sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl
en_core_web_trf @ https://github.com/explosion/spacy-models/releases/download/en_core_web_trf-3.8.0/en_core_web_trf-3.8.0-py3-none-any.whl
exceptiongroup==1.2.2
fastapi==0.115.9
filelock==3.18.0
flatbuffers==25.2.10
frozenlist==1.6.0
fsspec==2025.3.2
google-auth==2.40.0
googleapis-common-protos==1.70.0
greenlet==3.2.1
groq==0.24.0
grpcio==1.71.0
h11==0.14.0
hf-xet==1.0.5
httpcore==1.0.8
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.2
humanfriendly==10.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
Jinja2==3.1.6
joblib==1.5.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
kubernetes==32.0.1
langchain==0.3.24
langchain-community==0.3.22
langchain-core==0.3.55
langchain-groq==0.3.2
langchain-text-splitters==0.3.8
langcodes==3.5.0
langsmith==0.3.33
language_data==1.3.0
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
mdurl==0.1.2
mmh3==5.1.0
motor==3.7.0
mpmath==1.3.0
multidict==6.4.3
murmurhash==1.0.12
mypy_extensions==1.1.0
networkx==3.4.2
numpy==2.2.5
nvidia-cublas-cu12==12.6.4.1
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==9.5.1.17
nvidia-cufft-cu12==11.3.0.4
nvidia-cufile-cu12==1.11.1.6
nvidia-curand-cu12==10.3.7.77
nvidia-cusolver-cu12==11.7.1.2
nvidia-cusparse-cu12==12.5.4.2
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
oauthlib==3.2.2
onnxruntime==1.21.1
opentelemetry-api==1.32.1
opentelemetry-exporter-otlp-proto-common==1.32.1
opentelemetry-exporter-otlp-proto-grpc==1.32.1
opentelemetry-instrumentation==0.53b1
opentelemetry-instrumentation-asgi==0.53b1
opentelemetry-instrumentation-fastapi==0.53b1
opentelemetry-proto==1.32.1
opentelemetry-sdk==1.32.1
opentelemetry-semantic-conventions==0.53b1
opentelemetry-util-http==0.53b1
orjson==3.10.16
overrides==7.7.0
packaging==24.2
pillow==11.2.1
posthog==4.0.1
preshed==3.0.9
propcache==0.3.1
protobuf==5.29.4
psutil==7.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pydantic==2.11.3
pydantic-settings==2.9.1
pydantic_core==2.33.1
Pygments==2.19.1
pymongo==4.12.0
PyMuPDF==1.25.5
pypdf==5.4.0
PyPika==0.48.9
pyproject_hooks==1.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
PyYAML==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==14.0.0
rpds-py==0.24.0
rsa==4.9.1
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==4.1.0
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
sniffio==1.3.1
spacy==3.8.5
spacy-curated-transformers==0.3.0
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==2.0.40
srsly==2.5.1
starlette==0.45.3
sympy==1.14.0
tenacity==9.1.2
thinc==8.3.6
threadpoolctl==3.6.0
tokenizers==0.21.1
tomli==2.2.1
torch==2.7.0
tqdm==4.67.1
transformers==4.51.3
triton==3.3.0
typer==0.15.3
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.2
urllib3==2.4.0
uvicorn==0.34.2
uvloop==0.21.0
wasabi==1.1.3
watchfiles==1.0.5
weasel==0.4.1
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
yarl==1.20.0
zipp==3.21.0
zstandard==0.23.0
markdown
langgraph
import { configureStore } from "@reduxjs/toolkit";
import { HomeReducer, IHomeSlice } from "./slices/HomeSlice";
import { AskAIReducer, IAskAISlice } from "./slices/AskAISlice";

export interface IStore {
  homeReducer: IHomeSlice;
  AskAIReducer: IAskAISlice;
}

export const store = configureStore({
  reducer: {
    homeReducer: HomeReducer,
    AskAIReducer: AskAIReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
      immutableCheck: false,
    }),
});
